# "元亨利贞" 微信小程序完整设计方案

## 项目概述

### 软件名称
**元亨利贞**（取自《易经·乾卦》卦辞："乾，元亨利贞"）

### 项目定位
基于100G权威古籍知识库的专业AI命理分析平台，专注微信小程序生态，采用积分付费模式，为用户提供深度精准的命理服务。

### 核心特色
- 🤖 **AI驱动**：基于DeepSeek大模型的专业命理分析
- 📚 **权威知识库**：整合100GB古籍资料（易经49部、紫微斗数300+部等）
- 🎯 **深度分析**：精准推算流年事件、家庭结构、职业发展
- 💰 **积分系统**：新用户注册送10积分，邀请好友送50积分
- 🔮 **命盘终身卦象**：基于出生时间的终身卦象+时间起卦双重验证
- 📱 **微信生态**：专注微信小程序，集成微信支付

## 设计理念

### 核心原则
- **古籍为本**：所有功能严格基于知识库古籍内容，禁止胡编乱造
- **水墨至简**：纯黑白灰色调，极简设计美学
- **文化传承**：每个界面都体现传统文化底蕴
- **现代交互**：流畅动画，直观操作体验
- **用户体验**：界面设计和交互体验为最高优先级

### 视觉系统

#### 色彩方案
- **主色调**：墨黑 #1a1a1a / 淡墨 #666666 / 浅墨 #999999
- **背景色**：宣纸白 #fafafa / 古纸 #f8f8f0
- **强调色**：古金 #d4af37（仅用于重要提示）
- **水墨效果**：渐变晕染、墨迹飞白、纸张纹理

#### 字体系统
- **标题**：仿宋体，体现古典韵味
- **正文**：思源黑体，现代简洁易读
- **古籍引文**：楷体，突出文献权威性
- **数字**：等宽字体，确保对齐美观

## 知识库资料分析

### 📚 现有资料分类（基于实际知识库）

#### 1. 易经典籍（49部+21卷古今文全集）★★★★★
- **周易本义**（朱熹）- 宋代理学大师权威注解
- **伊川易传**（程颐）- 程朱理学易学经典
- **周易集注**（明·来知德）- 明代易学集大成之作
- **周易正义**（唐·孔颖达）- 官方权威注疏
- **周易古今文全集**（21卷）- 明杨时乔撰，集历代易学大成

#### 2. 紫微斗数（300+部典籍）★★★★★
- **陈希夷紫微斗数全书** - 紫微斗数开山之作
- **十八飞星策天紫微斗数全集** - 飞星派核心秘籍
- **紫云派系列**：斗数论命、论事业、论姻缘、论疾病等
- **中州派系列**（王亭之）：现代紫微斗数权威理论

#### 3. 子平八字（413册明抄本）★★★★☆
- **《子平遗书》**：413册明抄本，八字命理最权威典籍
- **历代实战案例**：古籍中记载的真实命例分析
- **格局理论**：严格按古籍记载的格局判断方法

#### 4. 梅花易数（邵雍原著）★★★☆☆
- **《梅花易数》**（邵雍）：宋代原著，梅花易数鼻祖
- **64卦详解**：完整卦象体系和应用方法
- **实战案例集**：历代梅花易数应用实例

## 积分系统设计

### 用户注册与积分获取
- **手机号注册**：10积分（需接收验证码）
- **邀请好友**：每成功邀请1人获得50积分
- **微信支付充值**：1积分 = 0.1元
- **账号绑定**：一个手机号对应一个账号，需接收验证码完成注册

### 功能积分消耗标准（根据资料完整性定价）

#### 免费功能（0积分）
- **古籍原文查询**：免费查阅所有古籍原文内容
- **古籍出处引证**：每个分析都提供具体古籍出处和原文依据

#### 基础功能（1积分）
- **梅花易数占卜**：按邵雍《梅花易数》时间起卦法（资料一般★★★☆☆）

#### 进阶功能（2积分）
- **子平八字分析**：基于《子平遗书》413册的四柱推命（资料较全★★★★☆）
  - 后台隐藏命盘终身卦象+时间起卦双重验证，确保准确性

#### 高级功能（3积分）
- **周易卦象查询**：基于49部易经典籍+21卷古今文全集（资料最全★★★★★）
- **紫微斗数详批**：基于300+部典籍全面分析（资料最全★★★★★）
  - 后台隐藏命盘终身卦象+时间起卦双重验证，确保准确性

#### 顶级功能（4积分）
- **综合命理会诊**：多种古籍理论交叉验证分析
- **流年运势精批**：结合周易卦象+八字+紫微的综合分析

### 充值套餐设计
```
积分充值套餐：
┌─────────────────────────────────┐
│ 基础套餐：10积分 = 1元           │
│ 超值套餐：50积分 = 4元 (8折)     │
│ 豪华套餐：100积分 = 7元 (7折)    │
│ 至尊套餐：200积分 = 12元 (6折)   │
└─────────────────────────────────┘
```

## 核心功能设计（严格基于知识库古籍）

### 1. 周易卦象模块（基于49部易经典籍+21卷古今文全集）

#### 古籍理论基础（资料最全★★★★★）
- **《周易本义》**（朱熹）：宋代理学权威注解，卦爻辞标准解释
- **《周易正义》**（孔颖达）：唐代官方注疏，历代公认权威
- **《伊川易传》**（程颐）：程朱理学经典，变卦理论完备
- **《周易古今文全集》**（21卷）：明杨时乔撰，集历代易学大成
- **《周易集注》**（来知德）：明代集大成之作，实用性强

#### 功能设计
- **六十四卦查询**：完整六十四卦体系，每卦都有古籍原文
- **起卦方式**：时间起卦、数字起卦、方位起卦（严格按古法）
- **卦象解析**：引用朱熹《本义》原文，不添加现代解释
- **变卦分析**：依据程颐《伊川易传》变卦理论
- **爻辞详解**：逐爻解释，引用孔颖达《正义》原文

### 2. 子平八字模块（基于《子平遗书》413册明抄本）

#### 古籍理论基础（资料较全★★★★☆）
- **《子平遗书》**：413册明抄本，八字命理最权威典籍
- **历代实战案例**：古籍中记载的真实命例分析
- **格局理论**：严格按古籍记载的格局判断方法
- **流年推算**：基于古籍流年理论，不添加现代内容

#### 功能设计（含命盘终身卦象+时间起卦双重验证）
- **四柱排盘**：按《子平遗书》标准方法排盘
- **格局判断**：严格依据古籍格局理论（正格、变格等）
- **十神分析**：基于古籍十神含义，不现代化解释
- **流年分析**：依据《子平遗书》流年法则推算
- **古籍原文**：每个分析都引用具体古籍条文
- **命盘终身卦象**：基于用户出生年月日时确定终身命运卦象
- **时间起卦验证**：结合当前时间起卦，与八字分析结果交叉验证

### 3. 紫微斗数模块（基于300+部斗数典籍）

#### 古籍理论基础（资料最全★★★★★）
- **《陈希夷紫微斗数全书》**：紫微斗数开山之作
- **《十八飞星策天紫微斗数全集》**：飞星派核心秘籍
- **紫云派系列**：斗数论命、论事业、论姻缘等专业典籍
- **中州派系列**（王亭之）：现代紫微斗数权威理论

#### 功能设计（含命盘终身卦象+时间起卦双重验证）
- **命盘排列**：按陈希夷原法排盘，不使用现代简化方法
- **十二宫分析**：严格按紫云派理论解析各宫位
- **四化飞星**：依据《十八飞星策天》法则分析
- **流年推算**：基于中州派流年理论，引用原文
- **星曜解释**：每颗星曜都有古籍出处和原文依据
- **命盘终身卦象**：基于用户出生年月日时确定终身命运卦象
- **时间起卦验证**：结合当前时间起卦，与紫微斗数分析结果交叉验证

### 4. 梅花易数模块（基于邵雍原著）

#### 古籍理论基础（资料一般★★★☆☆）
- **《梅花易数》**（邵雍）：宋代原著，梅花易数鼻祖
- **64卦详解**：完整卦象体系和应用方法
- **实战案例集**：历代梅花易数应用实例

#### 功能设计
- **时间起卦**：严格按邵雍《梅花易数》时间起卦法
- **数字起卦**：依据古法数字转卦象方法
- **卦象分析**：引用邵雍原著解释，不添加现代内容
- **应期推算**：按古籍记载的应期理论

### 5. 命盘终身卦象系统（核心创新）

#### 理论基础
系统在进行子平八字和紫微斗数分析时，后台会先"思考"用户的命盘终身卦象，然后再进行时间起卦验证。

#### 命盘终身卦象算法
```
基于用户出生时间的终身卦象：
年卦 = 出生年数 ÷ 8 取余数
月卦 = 出生月数 ÷ 8 取余数
日卦 = 出生日数 ÷ 8 取余数
时卦 = 出生时数 ÷ 8 取余数

终身上卦 = (年卦 + 月卦) ÷ 8 取余数
终身下卦 = (日卦 + 时卦) ÷ 8 取余数
终身卦象 = 由终身上卦和终身下卦组成的本命卦
```

#### 时间起卦验证算法
```
基于当前时间的验证卦象：
当前年月日时 → 按梅花易数起卦法
验证卦象 = 当前时间卦象

分析流程：
1. 系统首先确定用户的终身卦象（静态，代表一生基本格局）
2. 然后进行当前时间起卦（动态，代表当前状态）
3. 将终身卦象与时间卦象结合，验证八字/紫微分析结果
4. 确保分析的准确性和一致性
```

#### 应用场景
- **子平八字分析**：终身卦象反映命格基础，时间卦象验证当前运势
- **紫微斗数分析**：终身卦象印证命盘格局，时间卦象确认流年变化
- **综合分析**：多重验证确保分析结果的准确性和权威性

## 界面设计

### 1. 启动页面
```
┌─────────────────────────────────┐
│                                 │
│        ░░░ 水墨晕染背景 ░░░       │
│                                 │
│              元                 │
│        （水墨书法大字）           │
│                                 │
│         元亨利贞                 │
│      千年古籍 • AI智慧            │
│                                 │
│  ┌─────────────────────────────┐ │
│  │        微信授权登录          │ │
│  └─────────────────────────────┘ │
│                                 │
│      ～～～ 墨迹飞白效果 ～～～     │
└─────────────────────────────────┘
```

### 2. 主界面（简化功能模块）
```
┌─────────────────────────────────┐
│ 元亨利贞 • 古籍智慧传承           │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ 用户：{{微信名}} | 积分：8      │ │
│ │ 邀请好友获50积分 [分享]        │ │
│ └─────────────────────────────┘ │
│                                 │
│ 古籍命理模块：                   │
│                                 │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐│
│ │ 周易卦象 │ │ 子平八字 │ │ 紫微斗数 ││
│ │ 3积分   │ │ 2积分   │ │ 3积分   ││
│ └─────────┘ └─────────┘ └─────────┘│
│                                 │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐│
│ │ 梅花易数 │ │ 古籍查询 │ │ 积分充值 ││
│ │ 1积分   │ │ 免费   │ │ 微信   ││
│ └─────────┘ └─────────┘ └─────────┘│
└─────────────────────────────────┘
```

**微信名获取说明**：
- 使用微信小程序API `wx.getUserProfile()` 获取用户微信昵称
- 格式：`用户：{{微信名}}`，例如用户微信名为"小徐"，显示为"用户：小徐"
- 首次使用需要用户授权获取微信信息

### 3. 周易卦象界面
```
┌─────────────────────────────────┐
│ 周易卦象 • 朱熹《本义》           │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ 请选择起卦方式：              │ │
│ │ ○ 时间起卦（年月日时）        │ │
│ │ ○ 数字起卦（报三个数字）      │ │
│ │ ○ 方位起卦（选择方位）        │ │
│ └─────────────────────────────┘ │
│                                 │
│ 📅 时间起卦：                    │
│ ┌─────────────────────────────┐ │
│ │ 年：2024  月：03  日：15     │ │
│ │ 时：14:30                   │ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        [开始起卦]            │ │
│ └─────────────────────────────┘ │
│                                 │
│ 💡 基于《周易本义》朱熹注解       │
└─────────────────────────────────┘
```

### 4. 卦象结果界面
```
┌─────────────────────────────────┐
│ 卦象解析 • 《周易本义》           │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        乾为天 ☰☰            │ │
│ │                             │ │
│ │ 上卦：乾（天）               │ │
│ │ 下卦：乾（天）               │ │
│ │ 变爻：九五                   │ │
│ └─────────────────────────────┘ │
│                                 │
│ 📜 卦辞（朱熹注）：               │
│ "乾，元亨利贞。"                 │
│ 朱子曰："元者善之长，亨者嘉之会， │
│ 利者义之和，贞者事之干。"         │
│                                 │
│ 🔮 占断：                        │
│ 此卦大吉，君子自强不息，           │
│ 事业可成，但需持之以恒。           │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ [查看详解] [保存结果] [分享] │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### 5. 子平八字界面
```
┌─────────────────────────────────┐
│ 子平八字 • 《子平遗书》413册      │
│                                 │
│ 📅 请输入出生信息：               │
│ ┌─────────────────────────────┐ │
│ │ 公历：2024年3月15日14:30     │ │
│ │ 性别：○男 ○女               │ │
│ │ 地点：北京市                 │ │
│ └─────────────────────────────┘ │
│                                 │
│ 🔄 四柱排盘：                    │
│ ┌─────────────────────────────┐ │
│ │ 年柱：甲辰  月柱：丁卯        │ │
│ │ 日柱：己亥  时柱：辛未        │ │
│ │                             │ │
│ │ 五行：木土  火木  土水  金土  │ │
│ │ 纳音：佛灯火 炉中火 平地木 路旁土│ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        [开始分析]            │ │
│ └─────────────────────────────┘ │
│                                 │
│ 📚 依据《子平遗书》明抄本理论     │
└─────────────────────────────────┘
```

## 交互动效设计

### 页面转场
- **水墨渐变**：页面切换时如水墨晕染般自然过渡
- **墨迹飞白**：加载时的墨迹飞溅动画效果
- **纸张翻页**：古籍查阅时的翻页动画

### 按钮交互
- **墨迹扩散**：点击时从中心向外扩散墨迹效果
- **金色点亮**：重要按钮悬停时金色光晕效果
- **水波涟漪**：轻触反馈的水波纹动画

### 数据展示
- **毛笔书写**：文字逐字显示如毛笔书写
- **卦象变化**：卦爻变化的动态演示
- **星盘旋转**：紫微斗数命盘的缓慢旋转

## 用户体验优化

### 操作便捷性
- **一键分享**：结果可直接分享到微信
- **历史记录**：自动保存查询历史
- **收藏功能**：重要结果可收藏备查
- **语音输入**：支持语音输入问题

### 个性化设置
- **字体大小**：可调节文字大小
- **夜间模式**：深色水墨主题
- **动画开关**：可关闭动画效果
- **音效控制**：古典音效开关

### 无障碍支持
- **高对比度**：确保文字清晰可读
- **语音朗读**：支持屏幕阅读器
- **大字体模式**：老年用户友好
- **简化操作**：减少复杂手势

## 技术实现要点

### 水墨效果实现
- **CSS渐变**：多层渐变模拟水墨晕染
- **SVG动画**：矢量图形实现墨迹效果
- **Canvas绘制**：复杂水墨动画
- **滤镜效果**：模糊、透明度变化

### 性能优化
- **图片压缩**：水墨背景图优化
- **动画节流**：避免过度动画影响性能
- **懒加载**：按需加载界面元素
- **缓存策略**：合理缓存静态资源

### 兼容性考虑
- **微信版本**：兼容主流微信版本
- **设备适配**：不同屏幕尺寸适配
- **系统兼容**：iOS/Android双平台
- **网络优化**：弱网环境下的体验

## 技术架构设计

### 前端架构
- **框架选择**：微信小程序原生开发
- **UI框架**：WeUI + 自定义水墨风组件
- **状态管理**：微信小程序原生状态管理
- **网络请求**：wx.request + 封装的API层

### 后端架构
- **云开发**：微信云开发（数据库+云函数+存储）
- **AI模型**：DeepSeek API集成
- **知识库**：向量数据库存储古籍内容
- **支付系统**：微信支付API

### 数据库设计
```sql
-- 用户表
users: {
  openid: string,
  phone: string,
  nickname: string,
  credits: number,
  register_time: timestamp,
  invite_code: string
}

-- 分析记录表
fortune_records: {
  id: string,
  user_openid: string,
  type: string, // 'bazi', 'ziwei', 'yijing', 'meihua'
  birth_info: object,
  lifetime_gua: object, // 命盘终身卦象
  time_gua: object,     // 时间起卦
  analysis_result: text,
  credits_cost: number,
  create_time: timestamp
}

-- 邀请记录表
invitations: {
  inviter_openid: string,
  invitee_openid: string,
  credits_reward: number,
  create_time: timestamp
}
```

### 命盘终身卦象核心算法

#### JavaScript实现
```javascript
// 命盘终身卦象计算
function calculateLifetimeGua(birthInfo) {
  const { year, month, day, hour } = birthInfo;

  // 八卦数字对应
  const guaMap = ['坤', '震', '坎', '兑', '艮', '离', '巽', '乾'];

  // 计算年月日时对应的卦数
  const yearGua = year % 8;
  const monthGua = month % 8;
  const dayGua = day % 8;
  const hourGua = hour % 8;

  // 计算终身上下卦
  const upperGua = (yearGua + monthGua) % 8;
  const lowerGua = (dayGua + hourGua) % 8;

  // 生成64卦索引
  const guaIndex = upperGua * 8 + lowerGua;

  return {
    upperGua: guaMap[upperGua],
    lowerGua: guaMap[lowerGua],
    guaIndex: guaIndex,
    guaName: getGuaName(guaIndex), // 从64卦表中获取卦名
    description: '此为用户命盘终身卦象，代表一生基本格局'
  };
}

// 时间起卦计算
function calculateTimeGua() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const hour = now.getHours();

  // 按梅花易数时间起卦法
  const upperGua = (year + month + day) % 8;
  const lowerGua = (year + month + day + hour) % 8;
  const changingLine = (year + month + day + hour) % 6;

  return {
    originalGua: {
      upper: upperGua,
      lower: lowerGua,
      index: upperGua * 8 + lowerGua
    },
    changingLine: changingLine,
    description: '此为当前时间起卦，用于验证分析结果'
  };
}

// 双重卦象验证分析
function dualGuaAnalysis(lifetimeGua, timeGua, analysisType) {
  // 系统内部思考过程
  console.log(`系统思考：用户命盘终身卦象为${lifetimeGua.guaName}`);
  console.log(`当前时间起卦为${getGuaName(timeGua.originalGua.index)}`);

  // 根据分析类型（八字/紫微）进行验证
  const verification = {
    lifetimeConsistency: checkLifetimeConsistency(lifetimeGua, analysisType),
    timeRelevance: checkTimeRelevance(timeGua, analysisType),
    overallAccuracy: calculateAccuracy(lifetimeGua, timeGua)
  };

  return verification;
}
```

### AI分析流程
```javascript
// 子平八字分析流程（含双重卦象验证）
async function baziAnalysis(birthInfo) {
  // 1. 计算命盘终身卦象
  const lifetimeGua = calculateLifetimeGua(birthInfo);

  // 2. 当前时间起卦
  const timeGua = calculateTimeGua();

  // 3. 系统内部思考
  console.log(`系统分析：用户终身卦象${lifetimeGua.guaName}，代表其命格基础`);

  // 4. 八字排盘
  const baziChart = calculateBaziChart(birthInfo);

  // 5. AI分析（结合古籍知识库）
  const aiAnalysis = await callDeepSeekAPI({
    birthInfo,
    baziChart,
    lifetimeGua,
    timeGua,
    knowledgeBase: 'ziping_413_volumes'
  });

  // 6. 双重验证
  const verification = dualGuaAnalysis(lifetimeGua, timeGua, 'bazi');

  // 7. 返回完整分析结果
  return {
    baziChart,
    analysis: aiAnalysis,
    lifetimeGua,
    timeGua,
    verification,
    accuracy: verification.overallAccuracy
  };
}
```

## 开发规范

### 代码规范
- **组件命名**：使用PascalCase，如`FortuneCard`
- **样式类名**：使用kebab-case，如`fortune-card`
- **变量命名**：使用camelCase，如`userProfile`
- **常量命名**：使用UPPER_SNAKE_CASE，如`MAX_CREDITS`

### 文件结构
```
pages/
├── index/           # 主页
├── fortune/         # 算命功能页
├── profile/         # 个人中心
└── payment/         # 支付页面

components/
├── common/          # 通用组件
├── fortune/         # 算命相关组件
└── ui/              # UI组件

utils/
├── api.js          # API接口
├── fortune.js      # 算命逻辑（含命盘终身卦象）
├── gua.js          # 卦象计算逻辑
└── payment.js      # 支付逻辑

cloud/
├── functions/       # 云函数
│   ├── fortune/     # 算命分析云函数
│   ├── payment/     # 支付相关云函数
│   └── gua/         # 卦象计算云函数
└── database/        # 数据库集合
```

### 性能优化
- **图片懒加载**：使用微信小程序原生懒加载
- **分包加载**：按功能模块分包，减少首屏加载时间
- **缓存策略**：合理使用本地缓存，减少网络请求
- **代码分割**：按需加载，避免一次性加载过多代码
- **卦象预计算**：命盘终身卦象计算结果缓存，避免重复计算

---

**设计目标**：打造最具中国传统文化韵味的AI算命小程序，通过命盘终身卦象+时间起卦的双重验证机制，为用户提供既有文化底蕴又具现代感的精准命理分析体验。
